package com.travel.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 讲解点信息实体类
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("explanation_point")
@Schema(description = "讲解点信息")
public class ExplanationPoint {

    @Schema(description = "讲解点ID")
    @TableId(value = "point_id", type = IdType.AUTO)
    private Integer pointId;

    @Schema(description = "分类ID")
    @TableField("category_id")
    private Integer categoryId;

    @Schema(description = "讲解点标题")
    @TableField("title")
    private String title;

    @Schema(description = "位置标识")
    @TableField("position")
    private String position;

    @Schema(description = "讲解时长")
    @TableField("duration")
    private String duration;

    @Schema(description = "音频URL")
    @TableField("audio_url")
    private String audioUrl;

    @Schema(description = "封面图URL")
    @TableField("cover_url")
    private String coverUrl;

    @Schema(description = "图片数组(JSON格式)")
    @TableField("images")
    private String images;

    @Schema(description = "详细内容")
    @TableField("content")
    private String content;

    @Schema(description = "排序序号")
    @TableField("sort_order")
    private Integer sortOrder;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
