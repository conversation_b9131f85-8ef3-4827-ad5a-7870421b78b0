package com.travel.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.travel.dto.*;
import com.travel.entity.User;
import com.travel.exception.BusinessException;
import com.travel.mapper.UserMapper;
import com.travel.util.JwtUtil;
import com.travel.common.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * 用户服务类
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class UserService extends ServiceImpl<UserMapper, User> {

    @Autowired
    private WeChatService weChatService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 微信小程序登录
     */
    @Transactional(rollbackFor = Exception.class)
    public LoginResponse login(LoginRequest request) {
        // 1. 调用微信接口获取openid
        WeChatLoginResponse weChatResponse = weChatService.login(request.getCode());
        String openid = weChatResponse.getOpenid();

        // 2. 查询用户是否存在
        User existUser = getOne(new LambdaQueryWrapper<User>()
                .eq(User::getOpenid, openid));

        boolean isNewUser = false;
        User user;

        if (existUser == null) {
            // 3. 新用户注册
            user = new User();
            user.setOpenid(openid);
            user.setNickname(request.getNickname());
            user.setAvatarUrl(request.getAvatarUrl());
            user.setRegion(request.getRegion());
            user.setCreatedAt(LocalDateTime.now());
            
            save(user);
            isNewUser = true;
            log.info("新用户注册成功: openid={}, userId={}", openid, user.getUserId());
        } else {
            // 4. 更新用户信息
            user = existUser;
            if (StringUtils.hasText(request.getNickname())) {
                user.setNickname(request.getNickname());
            }
            if (StringUtils.hasText(request.getAvatarUrl())) {
                user.setAvatarUrl(request.getAvatarUrl());
            }
            if (StringUtils.hasText(request.getRegion())) {
                user.setRegion(request.getRegion());
            }
            updateById(user);
            log.info("用户信息更新成功: openid={}, userId={}", openid, user.getUserId());
        }

        // 5. 生成JWT Token
        String token = jwtUtil.generateToken(user.getUserId(), openid);

        // 6. 构建响应
        LoginResponse response = new LoginResponse();
        response.setToken(token);
        response.setUserId(user.getUserId());
        response.setNickname(user.getNickname());
        response.setAvatarUrl(user.getAvatarUrl());
        response.setIsNewUser(isNewUser);

        return response;
    }

    /**
     * 获取用户手机号
     */
    @Transactional(rollbackFor = Exception.class)
    public void bindPhone(Integer userId, String code) {
        // 1. 获取AccessToken
        String accessToken = weChatService.getAccessToken();

        // 2. 获取手机号
        WeChatPhoneResponse phoneResponse = weChatService.getPhoneNumber(accessToken, code);

        // 3. 更新用户手机号
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        user.setPhone(phoneResponse.getPurePhoneNumber());
        updateById(user);

        log.info("用户手机号绑定成功: userId={}, phone={}", userId, phoneResponse.getPurePhoneNumber());
    }

    /**
     * 根据用户ID获取用户信息
     */
    public User getUserById(Integer userId) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        return user;
    }

    /**
     * 根据OpenId获取用户信息
     */
    public User getUserByOpenid(String openid) {
        return getOne(new LambdaQueryWrapper<User>()
                .eq(User::getOpenid, openid));
    }
}
