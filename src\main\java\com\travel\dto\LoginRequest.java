package com.travel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 登录请求DTO
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@Schema(description = "登录请求")
public class LoginRequest {

    @Schema(description = "微信登录凭证code", required = true)
    @NotBlank(message = "登录凭证不能为空")
    private String code;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户头像URL")
    private String avatarUrl;

    @Schema(description = "用户地区")
    private String region;
}
