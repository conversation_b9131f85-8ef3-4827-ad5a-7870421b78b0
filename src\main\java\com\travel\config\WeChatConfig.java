package com.travel.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 微信小程序配置
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wechat.miniprogram")
public class WeChatConfig {
    
    /**
     * 小程序AppId
     */
    private String appId;
    
    /**
     * 小程序AppSecret
     */
    private String appSecret;
    
    /**
     * 微信登录接口URL
     */
    private String loginUrl;
    
    /**
     * 获取手机号接口URL
     */
    private String phoneUrl;
}
