# 旅游讲解小程序后端服务

基于Spring Boot开发的微信小程序旅游讲解服务后端API，提供完整的用户认证、产品管理、音频讲解等功能。

## 🚀 项目特性

- **微信小程序集成**: 支持微信登录、手机号获取
- **JWT认证**: 安全的用户认证和授权机制
- **RESTful API**: 标准的REST接口设计
- **Swagger文档**: 完整的API文档和在线测试
- **MyBatis-Plus**: 高效的数据库操作
- **全局异常处理**: 统一的错误处理机制
- **参数校验**: 完善的输入验证
- **CORS支持**: 跨域请求支持

## 📋 技术栈

- **框架**: Spring Boot 2.7.14
- **数据库**: MySQL 8.0
- **ORM**: MyBatis-Plus 3.5.3.1
- **认证**: JWT (jjwt 0.11.5)
- **文档**: SpringDoc OpenAPI 3
- **工具**: Hutool, FastJSON2
- **缓存**: Redis (可选)

## 🗄️ 数据库设计

项目包含以下核心表结构：

- `user` - 用户信息表
- `city` - 城市信息表
- `product` - 产品信息表
- `lecturer` - 讲师信息表
- `attraction_category` - 景区分类表
- `explanation_point` - 讲解点信息表
- `attraction_detail` - 景区详情表
- `order` / `order_item` - 订单相关表
- `review` - 用户评价表
- `news` - 资讯信息表

## 🔧 环境配置

### 1. 数据库配置

```yaml
spring:
  datasource:
    url: ***********************************************************************************************************************************************
    username: your_username
    password: your_password
```

### 2. 微信小程序配置

```yaml
wechat:
  miniprogram:
    app-id: your_wechat_app_id
    app-secret: your_wechat_app_secret
```

### 3. JWT配置

```yaml
jwt:
  secret: your_jwt_secret_key
  expiration: 604800  # 7天
```

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd miniprogram-backend
```

### 2. 导入数据库

```bash
mysql -u root -p travel < travel.sql
```

### 3. 配置环境变量

创建 `application-local.yml` 文件，配置本地环境参数：

```yaml
spring:
  datasource:
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:your_password}

wechat:
  miniprogram:
    app-id: ${WECHAT_APP_ID:your_app_id}
    app-secret: ${WECHAT_APP_SECRET:your_app_secret}

jwt:
  secret: ${JWT_SECRET:your_secret_key}
```

### 4. 启动项目

```bash
mvn spring-boot:run
```

或者使用IDE直接运行 `TravelApplication.java`

### 5. 访问API文档

启动成功后访问：http://localhost:8080/swagger-ui.html

## 📚 API接口

### 认证相关
- `POST /api/auth/login` - 微信小程序登录
- `POST /api/auth/bind-phone` - 绑定手机号

### 用户管理
- `GET /api/user/profile` - 获取用户信息
- `PUT /api/user/profile` - 更新用户信息

### 城市管理
- `GET /api/cities` - 获取城市列表
- `GET /api/cities/{cityId}` - 获取城市详情
- `GET /api/cities/search` - 搜索城市

### 产品管理
- `GET /api/products/page` - 分页查询产品
- `GET /api/products/{productId}` - 获取产品详情
- `GET /api/products/city/{cityId}` - 根据城市获取产品
- `GET /api/products/recommend` - 获取推荐产品

### 讲解内容
- `GET /api/explanation/product/{productId}/points` - 获取产品讲解点
- `GET /api/explanation/point/{pointId}` - 获取讲解点详情
- `GET /api/explanation/category/{categoryId}/points` - 获取分类讲解点

### 系统监控
- `GET /api/health` - 健康检查

## 🔐 认证机制

项目使用JWT进行用户认证：

1. 用户通过微信登录获取Token
2. 后续请求需在Header中携带Token：`Authorization: Bearer <token>`
3. 部分公开接口无需认证（如城市列表、产品列表等）

## 📝 开发规范

### 响应格式

所有API响应统一使用以下格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1641456000000
}
```

### 错误码

- `200` - 操作成功
- `400` - 参数错误
- `401` - 未授权
- `404` - 资源不存在
- `500` - 系统错误
- `1001-1999` - 用户相关错误
- `2001-2999` - 产品相关错误
- `3001-3999` - 订单相关错误

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 邮箱: <EMAIL>
