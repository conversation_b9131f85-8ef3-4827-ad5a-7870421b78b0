package com.travel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 微信手机号响应DTO
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@Schema(description = "微信手机号响应")
public class WeChatPhoneResponse {

    @Schema(description = "用户绑定的手机号（国外手机号会有区号）")
    private String phoneNumber;

    @Schema(description = "没有区号的手机号")
    private String purePhoneNumber;

    @Schema(description = "区号")
    private String countryCode;
}
