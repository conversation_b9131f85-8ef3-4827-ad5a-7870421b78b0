@echo off
echo ================================
echo 旅游讲解小程序后端服务启动脚本
echo ================================

echo 正在检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请先安装JDK 8或更高版本
    pause
    exit /b 1
)

echo.
echo 正在检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo 错误：未找到Maven环境，请先安装Maven
    pause
    exit /b 1
)

echo.
echo 正在编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo 编译失败，请检查代码
    pause
    exit /b 1
)

echo.
echo 正在启动服务...
echo 请确保MySQL数据库已启动并导入了travel.sql文件
echo 服务启动后可访问：
echo - API文档: http://localhost:8080/swagger-ui.html
echo - 健康检查: http://localhost:8080/api/health
echo.

mvn spring-boot:run

pause
