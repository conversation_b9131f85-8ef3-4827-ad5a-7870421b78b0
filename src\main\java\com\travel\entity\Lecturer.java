package com.travel.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 讲师信息实体类
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("lecturer")
@Schema(description = "讲师信息")
public class Lecturer {

    @Schema(description = "讲师ID")
    @TableId(value = "lecturer_id", type = IdType.AUTO)
    private Integer lecturerId;

    @Schema(description = "讲师姓名")
    @TableField("name")
    private String name;

    @Schema(description = "讲师头像URL")
    @TableField("avatar_url")
    private String avatarUrl;

    @Schema(description = "头衔")
    @TableField("title")
    private String title;

    @Schema(description = "讲师简介")
    @TableField("intro")
    private String intro;

    @Schema(description = "专长领域")
    @TableField("expertise")
    private String expertise;
}
