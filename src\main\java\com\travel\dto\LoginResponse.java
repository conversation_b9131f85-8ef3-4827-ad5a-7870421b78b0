package com.travel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 登录响应DTO
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@Schema(description = "登录响应")
public class LoginResponse {

    @Schema(description = "JWT访问令牌")
    private String token;

    @Schema(description = "用户ID")
    private Integer userId;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户头像URL")
    private String avatarUrl;

    @Schema(description = "是否为新用户")
    private Boolean isNewUser;
}
