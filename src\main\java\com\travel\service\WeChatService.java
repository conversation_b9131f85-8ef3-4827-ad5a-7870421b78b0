package com.travel.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.travel.config.WeChatConfig;
import com.travel.dto.WeChatLoginResponse;
import com.travel.dto.WeChatPhoneResponse;
import com.travel.exception.BusinessException;
import com.travel.common.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * 微信服务类
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class WeChatService {

    @Autowired
    private WeChatConfig weChatConfig;

    private final WebClient webClient = WebClient.builder().build();

    /**
     * 微信小程序登录
     */
    public WeChatLoginResponse login(String code) {
        try {
            String url = String.format("%s?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                    weChatConfig.getLoginUrl(),
                    weChatConfig.getAppId(),
                    weChatConfig.getAppSecret(),
                    code);

            Mono<String> response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(String.class);

            String result = response.block();
            log.info("微信登录响应: {}", result);

            JSONObject jsonObject = JSON.parseObject(result);
            
            if (jsonObject.containsKey("errcode")) {
                Integer errcode = jsonObject.getInteger("errcode");
                String errmsg = jsonObject.getString("errmsg");
                log.error("微信登录失败: errcode={}, errmsg={}", errcode, errmsg);
                throw new BusinessException(ResultCode.WECHAT_LOGIN_FAILED, errmsg);
            }

            WeChatLoginResponse loginResponse = new WeChatLoginResponse();
            loginResponse.setOpenid(jsonObject.getString("openid"));
            loginResponse.setSessionKey(jsonObject.getString("session_key"));
            loginResponse.setUnionid(jsonObject.getString("unionid"));

            return loginResponse;
        } catch (Exception e) {
            log.error("微信登录异常", e);
            throw new BusinessException(ResultCode.WECHAT_LOGIN_FAILED, "微信登录失败");
        }
    }

    /**
     * 获取微信用户手机号
     */
    public WeChatPhoneResponse getPhoneNumber(String accessToken, String code) {
        try {
            String url = String.format("%s?access_token=%s", weChatConfig.getPhoneUrl(), accessToken);
            
            JSONObject requestBody = new JSONObject();
            requestBody.put("code", code);

            Mono<String> response = webClient.post()
                    .uri(url)
                    .bodyValue(requestBody.toJSONString())
                    .retrieve()
                    .bodyToMono(String.class);

            String result = response.block();
            log.info("获取手机号响应: {}", result);

            JSONObject jsonObject = JSON.parseObject(result);
            
            if (jsonObject.containsKey("errcode") && !jsonObject.getInteger("errcode").equals(0)) {
                Integer errcode = jsonObject.getInteger("errcode");
                String errmsg = jsonObject.getString("errmsg");
                log.error("获取手机号失败: errcode={}, errmsg={}", errcode, errmsg);
                throw new BusinessException(ResultCode.PHONE_GET_FAILED, errmsg);
            }

            JSONObject phoneInfo = jsonObject.getJSONObject("phone_info");
            WeChatPhoneResponse phoneResponse = new WeChatPhoneResponse();
            phoneResponse.setPhoneNumber(phoneInfo.getString("phoneNumber"));
            phoneResponse.setPurePhoneNumber(phoneInfo.getString("purePhoneNumber"));
            phoneResponse.setCountryCode(phoneInfo.getString("countryCode"));

            return phoneResponse;
        } catch (Exception e) {
            log.error("获取手机号异常", e);
            throw new BusinessException(ResultCode.PHONE_GET_FAILED, "获取手机号失败");
        }
    }

    /**
     * 获取小程序全局唯一后台接口调用凭据
     */
    public String getAccessToken() {
        try {
            String url = String.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s",
                    weChatConfig.getAppId(),
                    weChatConfig.getAppSecret());

            Mono<String> response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(String.class);

            String result = response.block();
            JSONObject jsonObject = JSON.parseObject(result);
            
            if (jsonObject.containsKey("errcode")) {
                Integer errcode = jsonObject.getInteger("errcode");
                String errmsg = jsonObject.getString("errmsg");
                log.error("获取AccessToken失败: errcode={}, errmsg={}", errcode, errmsg);
                throw new BusinessException(ResultCode.THIRD_PARTY_ERROR, errmsg);
            }

            return jsonObject.getString("access_token");
        } catch (Exception e) {
            log.error("获取AccessToken异常", e);
            throw new BusinessException(ResultCode.THIRD_PARTY_ERROR, "获取AccessToken失败");
        }
    }
}
