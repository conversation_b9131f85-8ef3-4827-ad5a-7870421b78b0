package com.travel.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.travel.config.WeChatConfig;
import com.travel.dto.WeChatLoginResponse;
import com.travel.dto.WeChatPhoneResponse;
import com.travel.exception.BusinessException;
import com.travel.common.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * 微信服务类
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class WeChatService {

    @Autowired
    private WeChatConfig weChatConfig;

    private final WebClient webClient = WebClient.builder().build();
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 微信小程序登录
     */
    public WeChatLoginResponse login(String code) {
        try {
            String url = String.format("%s?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                    weChatConfig.getLoginUrl(),
                    weChatConfig.getAppId(),
                    weChatConfig.getAppSecret(),
                    code);

            Mono<String> response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(String.class);

            String result = response.block();
            log.info("微信登录响应: {}", result);

            JsonNode jsonNode = objectMapper.readTree(result);

            if (jsonNode.has("errcode")) {
                Integer errcode = jsonNode.get("errcode").asInt();
                String errmsg = jsonNode.get("errmsg").asText();
                log.error("微信登录失败: errcode={}, errmsg={}", errcode, errmsg);
                throw new BusinessException(ResultCode.WECHAT_LOGIN_FAILED, errmsg);
            }

            WeChatLoginResponse loginResponse = new WeChatLoginResponse();
            loginResponse.setOpenid(jsonNode.get("openid").asText());
            loginResponse.setSessionKey(jsonNode.get("session_key").asText());
            loginResponse.setUnionid(jsonNode.has("unionid") ? jsonNode.get("unionid").asText() : null);

            return loginResponse;
        } catch (Exception e) {
            log.error("微信登录异常", e);
            throw new BusinessException(ResultCode.WECHAT_LOGIN_FAILED, "微信登录失败");
        }
    }

    /**
     * 获取微信用户手机号
     */
    public WeChatPhoneResponse getPhoneNumber(String accessToken, String code) {
        try {
            String url = String.format("%s?access_token=%s", weChatConfig.getPhoneUrl(), accessToken);

            String requestBody = String.format("{\"code\":\"%s\"}", code);

            Mono<String> response = webClient.post()
                    .uri(url)
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class);

            String result = response.block();
            log.info("获取手机号响应: {}", result);

            JsonNode jsonNode = objectMapper.readTree(result);

            if (jsonNode.has("errcode") && jsonNode.get("errcode").asInt() != 0) {
                Integer errcode = jsonNode.get("errcode").asInt();
                String errmsg = jsonNode.get("errmsg").asText();
                log.error("获取手机号失败: errcode={}, errmsg={}", errcode, errmsg);
                throw new BusinessException(ResultCode.PHONE_GET_FAILED, errmsg);
            }

            JsonNode phoneInfo = jsonNode.get("phone_info");
            WeChatPhoneResponse phoneResponse = new WeChatPhoneResponse();
            phoneResponse.setPhoneNumber(phoneInfo.get("phoneNumber").asText());
            phoneResponse.setPurePhoneNumber(phoneInfo.get("purePhoneNumber").asText());
            phoneResponse.setCountryCode(phoneInfo.get("countryCode").asText());

            return phoneResponse;
        } catch (Exception e) {
            log.error("获取手机号异常", e);
            throw new BusinessException(ResultCode.PHONE_GET_FAILED, "获取手机号失败");
        }
    }

    /**
     * 获取小程序全局唯一后台接口调用凭据
     */
    public String getAccessToken() {
        try {
            String url = String.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s",
                    weChatConfig.getAppId(),
                    weChatConfig.getAppSecret());

            Mono<String> response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(String.class);

            String result = response.block();
            JsonNode jsonNode = objectMapper.readTree(result);

            if (jsonNode.has("errcode")) {
                Integer errcode = jsonNode.get("errcode").asInt();
                String errmsg = jsonNode.get("errmsg").asText();
                log.error("获取AccessToken失败: errcode={}, errmsg={}", errcode, errmsg);
                throw new BusinessException(ResultCode.THIRD_PARTY_ERROR, errmsg);
            }

            return jsonNode.get("access_token").asText();
        } catch (Exception e) {
            log.error("获取AccessToken异常", e);
            throw new BusinessException(ResultCode.THIRD_PARTY_ERROR, "获取AccessToken失败");
        }
    }
}
