server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: travel-miniprogram-backend
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:xiaoma}
    
  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # JSON配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/*.xml

# 微信小程序配置
wechat:
  miniprogram:
    app-id: ${WECHAT_APP_ID:your_app_id}
    app-secret: ${WECHAT_APP_SECRET:your_app_secret}
    login-url: https://api.weixin.qq.com/sns/jscode2session
    phone-url: https://api.weixin.qq.com/wxa/business/getuserphonenumber

# JWT配置
jwt:
  secret: ${JWT_SECRET:travel_miniprogram_secret_key_2025}
  expiration: 604800 # 7天，单位秒

# 文件上传配置
file:
  upload:
    path: ${FILE_UPLOAD_PATH:/uploads/}
    max-size: 10MB

# 日志配置
logging:
  level:
    com.travel: debug
    org.springframework.web: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# Swagger配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: com.travel.controller
