package com.travel.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.travel.entity.City;
import com.travel.exception.BusinessException;
import com.travel.mapper.CityMapper;
import com.travel.common.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 城市服务类
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class CityService extends ServiceImpl<CityMapper, City> {

    /**
     * 获取所有城市列表
     */
    public List<City> getAllCities() {
        return list();
    }

    /**
     * 根据城市名称搜索
     */
    public List<City> searchCitiesByName(String name) {
        if (!StringUtils.hasText(name)) {
            return getAllCities();
        }
        
        LambdaQueryWrapper<City> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(City::getName, name);
        return list(queryWrapper);
    }

    /**
     * 获取城市详情
     */
    public City getCityDetail(Integer cityId) {
        City city = getById(cityId);
        if (city == null) {
            throw new BusinessException(ResultCode.CITY_NOT_FOUND);
        }
        return city;
    }

    /**
     * 根据城市名称获取城市信息
     */
    public City getCityByName(String name) {
        LambdaQueryWrapper<City> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(City::getName, name);
        return getOne(queryWrapper);
    }
}
