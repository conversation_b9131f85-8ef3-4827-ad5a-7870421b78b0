package com.travel.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.travel.entity.Product;
import com.travel.exception.BusinessException;
import com.travel.mapper.ProductMapper;
import com.travel.common.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 产品服务类
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class ProductService extends ServiceImpl<ProductMapper, Product> {

    /**
     * 分页查询产品列表
     */
    public IPage<Product> getProductPage(Integer current, Integer size, Integer cityId, Integer type, String keyword) {
        Page<Product> page = new Page<>(current, size);
        
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        
        // 城市筛选
        if (cityId != null) {
            queryWrapper.eq(Product::getCityId, cityId);
        }
        
        // 类型筛选
        if (type != null) {
            queryWrapper.eq(Product::getType, type);
        }
        
        // 关键词搜索
        if (StringUtils.hasText(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(Product::getName, keyword)
                    .or()
                    .like(Product::getDescription, keyword)
                    .or()
                    .like(Product::getTags, keyword));
        }
        
        // 按创建时间倒序
        queryWrapper.orderByDesc(Product::getCreatedAt);
        
        return page(page, queryWrapper);
    }

    /**
     * 根据城市ID获取产品列表
     */
    public List<Product> getProductsByCity(Integer cityId) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getCityId, cityId)
                .orderByDesc(Product::getCreatedAt);
        return list(queryWrapper);
    }

    /**
     * 根据类型获取产品列表
     */
    public List<Product> getProductsByType(Integer type) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getType, type)
                .orderByDesc(Product::getCreatedAt);
        return list(queryWrapper);
    }

    /**
     * 获取产品详情
     */
    public Product getProductDetail(Integer productId) {
        Product product = getById(productId);
        if (product == null) {
            throw new BusinessException(ResultCode.PRODUCT_NOT_FOUND);
        }
        return product;
    }

    /**
     * 获取推荐产品列表
     */
    public List<Product> getRecommendProducts(Integer limit) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(Product::getCreatedAt)
                .last("LIMIT " + (limit != null ? limit : 10));
        return list(queryWrapper);
    }

    /**
     * 根据讲师ID获取产品列表
     */
    public List<Product> getProductsByLecturer(Integer lecturerId) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getLecturerId, lecturerId)
                .orderByDesc(Product::getCreatedAt);
        return list(queryWrapper);
    }
}
