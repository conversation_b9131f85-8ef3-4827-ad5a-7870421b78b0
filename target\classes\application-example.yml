# 环境配置示例文件
# 复制此文件为 application-local.yml 并修改相应配置

server:
  port: 8080

spring:
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: root
    password: 123456
    
  # Redis配置（可选）
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0

# 微信小程序配置
wechat:
  miniprogram:
    app-id: wx1234567890abcdef  # 替换为你的小程序AppId
    app-secret: your_app_secret_here  # 替换为你的小程序AppSecret

# JWT配置
jwt:
  secret: travel_miniprogram_secret_key_2025_change_this_in_production
  expiration: 604800  # 7天，单位秒

# 文件上传配置
file:
  upload:
    path: /uploads/
    max-size: 10MB

# 日志配置
logging:
  level:
    com.travel: debug
    root: info
  file:
    name: logs/travel-backend.log
