package com.travel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 微信登录响应DTO
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@Schema(description = "微信登录响应")
public class WeChatLoginResponse {

    @Schema(description = "用户唯一标识")
    private String openid;

    @Schema(description = "会话密钥")
    private String sessionKey;

    @Schema(description = "用户在开放平台的唯一标识符")
    private String unionid;
}
