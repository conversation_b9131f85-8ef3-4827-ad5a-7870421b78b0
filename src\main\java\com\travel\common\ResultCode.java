package com.travel.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Getter
@AllArgsConstructor
public enum ResultCode {
    
    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    
    // 用户相关
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_DISABLED(1002, "用户已被禁用"),
    LOGIN_FAILED(1003, "登录失败"),
    TOKEN_INVALID(1004, "Token无效"),
    TOKEN_EXPIRED(1005, "Token已过期"),
    WECHAT_LOGIN_FAILED(1006, "微信登录失败"),
    PHONE_GET_FAILED(1007, "获取手机号失败"),
    
    // 产品相关
    PRODUCT_NOT_FOUND(2001, "产品不存在"),
    PRODUCT_DISABLED(2002, "产品已下架"),
    CATEGORY_NOT_FOUND(2003, "分类不存在"),
    
    // 订单相关
    ORDER_NOT_FOUND(3001, "订单不存在"),
    ORDER_STATUS_ERROR(3002, "订单状态错误"),
    ORDER_CREATE_FAILED(3003, "订单创建失败"),
    ORDER_PAY_FAILED(3004, "订单支付失败"),
    
    // 文件相关
    FILE_UPLOAD_FAILED(4001, "文件上传失败"),
    FILE_TYPE_ERROR(4002, "文件类型错误"),
    FILE_SIZE_ERROR(4003, "文件大小超限"),
    
    // 业务相关
    CITY_NOT_FOUND(5001, "城市不存在"),
    LECTURER_NOT_FOUND(5002, "讲师不存在"),
    REVIEW_ALREADY_EXISTS(5003, "已评价过该产品"),
    
    // 系统相关
    SYSTEM_ERROR(9001, "系统错误"),
    DATABASE_ERROR(9002, "数据库错误"),
    REDIS_ERROR(9003, "Redis错误"),
    THIRD_PARTY_ERROR(9004, "第三方服务错误");
    
    private final Integer code;
    private final String message;
}
