package com.travel.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.travel.entity.AttractionCategory;
import com.travel.entity.ExplanationPoint;
import com.travel.mapper.AttractionCategoryMapper;
import com.travel.mapper.ExplanationPointMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 讲解点服务类
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class ExplanationPointService extends ServiceImpl<ExplanationPointMapper, ExplanationPoint> {

    @Autowired
    private AttractionCategoryMapper categoryMapper;

    /**
     * 根据产品ID获取所有分类和讲解点
     */
    public List<AttractionCategory> getCategoriesWithPointsByProduct(Integer productId) {
        // 1. 获取产品的所有分类
        LambdaQueryWrapper<AttractionCategory> categoryWrapper = new LambdaQueryWrapper<>();
        categoryWrapper.eq(AttractionCategory::getProductId, productId)
                .orderByAsc(AttractionCategory::getSortOrder);
        List<AttractionCategory> categories = categoryMapper.selectList(categoryWrapper);

        // 2. 为每个分类获取讲解点
        for (AttractionCategory category : categories) {
            List<ExplanationPoint> points = getPointsByCategory(category.getCategoryId());
            // 这里可以设置一个临时字段存储讲解点，或者使用VO对象
        }

        return categories;
    }

    /**
     * 根据分类ID获取讲解点列表
     */
    public List<ExplanationPoint> getPointsByCategory(Integer categoryId) {
        LambdaQueryWrapper<ExplanationPoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExplanationPoint::getCategoryId, categoryId)
                .orderByAsc(ExplanationPoint::getSortOrder);
        return list(queryWrapper);
    }

    /**
     * 根据产品ID获取所有讲解点
     */
    public List<ExplanationPoint> getPointsByProduct(Integer productId) {
        // 1. 先获取产品的所有分类ID
        LambdaQueryWrapper<AttractionCategory> categoryWrapper = new LambdaQueryWrapper<>();
        categoryWrapper.eq(AttractionCategory::getProductId, productId);
        List<AttractionCategory> categories = categoryMapper.selectList(categoryWrapper);

        if (categories.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 获取所有分类的讲解点
        List<Integer> categoryIds = categories.stream()
                .map(AttractionCategory::getCategoryId)
                .collect(Collectors.toList());

        LambdaQueryWrapper<ExplanationPoint> pointWrapper = new LambdaQueryWrapper<>();
        pointWrapper.in(ExplanationPoint::getCategoryId, categoryIds)
                .orderByAsc(ExplanationPoint::getSortOrder);
        
        return list(pointWrapper);
    }

    /**
     * 获取讲解点详情
     */
    public ExplanationPoint getPointDetail(Integer pointId) {
        return getById(pointId);
    }

    /**
     * 根据位置搜索讲解点
     */
    public List<ExplanationPoint> searchPointsByPosition(String position) {
        LambdaQueryWrapper<ExplanationPoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(ExplanationPoint::getPosition, position)
                .orderByAsc(ExplanationPoint::getSortOrder);
        return list(queryWrapper);
    }
}
