package com.travel.controller;

import com.travel.common.Result;
import com.travel.entity.User;
import com.travel.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户控制器
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/user")
@Tag(name = "用户管理", description = "用户信息相关接口")
@SecurityRequirement(name = "Bearer")
@Validated
public class UserController {

    @Autowired
    private UserService userService;

    @Operation(summary = "获取当前用户信息", description = "根据Token获取当前登录用户的详细信息")
    @GetMapping("/profile")
    public Result<User> getUserProfile(HttpServletRequest request) {
        Integer userId = (Integer) request.getAttribute("userId");
        log.info("获取用户信息: userId={}", userId);
        
        User user = userService.getUserById(userId);
        return Result.success(user);
    }

    @Operation(summary = "更新用户信息", description = "更新当前用户的基本信息")
    @PutMapping("/profile")
    public Result<Void> updateUserProfile(
            HttpServletRequest request,
            @Parameter(description = "用户昵称") @RequestParam(required = false) String nickname,
            @Parameter(description = "用户头像URL") @RequestParam(required = false) String avatarUrl,
            @Parameter(description = "用户地区") @RequestParam(required = false) String region) {
        
        Integer userId = (Integer) request.getAttribute("userId");
        log.info("更新用户信息: userId={}, nickname={}, avatarUrl={}, region={}", 
                userId, nickname, avatarUrl, region);
        
        User user = userService.getUserById(userId);
        
        if (nickname != null) {
            user.setNickname(nickname);
        }
        if (avatarUrl != null) {
            user.setAvatarUrl(avatarUrl);
        }
        if (region != null) {
            user.setRegion(region);
        }
        
        userService.updateById(user);
        return Result.success("用户信息更新成功");
    }
}
