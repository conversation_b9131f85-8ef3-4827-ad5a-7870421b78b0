package com.travel.controller;

import com.travel.common.Result;
import com.travel.entity.City;
import com.travel.service.CityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 城市控制器
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/cities")
@Tag(name = "城市管理", description = "城市信息相关接口")
@Validated
public class CityController {

    @Autowired
    private CityService cityService;

    @Operation(summary = "获取所有城市列表", description = "获取系统中所有城市信息")
    @GetMapping
    public Result<List<City>> getAllCities() {
        log.info("获取所有城市列表");
        List<City> cities = cityService.getAllCities();
        return Result.success(cities);
    }

    @Operation(summary = "搜索城市", description = "根据城市名称搜索城市")
    @GetMapping("/search")
    public Result<List<City>> searchCities(
            @Parameter(description = "城市名称关键词") @RequestParam(required = false) String name) {
        
        log.info("搜索城市: name={}", name);
        List<City> cities = cityService.searchCitiesByName(name);
        return Result.success(cities);
    }

    @Operation(summary = "获取城市详情", description = "根据城市ID获取详细信息")
    @GetMapping("/{cityId}")
    public Result<City> getCityDetail(
            @Parameter(description = "城市ID", required = true) @PathVariable @NotNull Integer cityId) {
        
        log.info("获取城市详情: cityId={}", cityId);
        City city = cityService.getCityDetail(cityId);
        return Result.success(city);
    }
}
