package com.travel.controller;

import com.travel.common.Result;
import com.travel.entity.AttractionCategory;
import com.travel.entity.ExplanationPoint;
import com.travel.service.ExplanationPointService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 讲解内容控制器
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/explanation")
@Tag(name = "讲解内容", description = "音频讲解相关接口")
@Validated
public class ExplanationController {

    @Autowired
    private ExplanationPointService explanationPointService;

    @Operation(summary = "获取产品的分类和讲解点", description = "根据产品ID获取所有分类及其讲解点")
    @GetMapping("/product/{productId}/categories")
    public Result<List<AttractionCategory>> getCategoriesWithPoints(
            @Parameter(description = "产品ID", required = true) @PathVariable @NotNull Integer productId) {
        
        log.info("获取产品分类和讲解点: productId={}", productId);
        List<AttractionCategory> categories = explanationPointService.getCategoriesWithPointsByProduct(productId);
        return Result.success(categories);
    }

    @Operation(summary = "获取分类下的讲解点", description = "根据分类ID获取所有讲解点")
    @GetMapping("/category/{categoryId}/points")
    public Result<List<ExplanationPoint>> getPointsByCategory(
            @Parameter(description = "分类ID", required = true) @PathVariable @NotNull Integer categoryId) {
        
        log.info("获取分类讲解点: categoryId={}", categoryId);
        List<ExplanationPoint> points = explanationPointService.getPointsByCategory(categoryId);
        return Result.success(points);
    }

    @Operation(summary = "获取产品的所有讲解点", description = "根据产品ID获取所有讲解点")
    @GetMapping("/product/{productId}/points")
    public Result<List<ExplanationPoint>> getPointsByProduct(
            @Parameter(description = "产品ID", required = true) @PathVariable @NotNull Integer productId) {
        
        log.info("获取产品所有讲解点: productId={}", productId);
        List<ExplanationPoint> points = explanationPointService.getPointsByProduct(productId);
        return Result.success(points);
    }

    @Operation(summary = "获取讲解点详情", description = "根据讲解点ID获取详细信息")
    @GetMapping("/point/{pointId}")
    public Result<ExplanationPoint> getPointDetail(
            @Parameter(description = "讲解点ID", required = true) @PathVariable @NotNull Integer pointId) {
        
        log.info("获取讲解点详情: pointId={}", pointId);
        ExplanationPoint point = explanationPointService.getPointDetail(pointId);
        return Result.success(point);
    }

    @Operation(summary = "根据位置搜索讲解点", description = "根据位置关键词搜索讲解点")
    @GetMapping("/search")
    public Result<List<ExplanationPoint>> searchPointsByPosition(
            @Parameter(description = "位置关键词") @RequestParam(required = false) String position) {
        
        log.info("搜索讲解点: position={}", position);
        List<ExplanationPoint> points = explanationPointService.searchPointsByPosition(position);
        return Result.success(points);
    }
}
