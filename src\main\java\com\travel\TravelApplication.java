package com.travel;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 旅游讲解小程序后端服务启动类
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@SpringBootApplication
@MapperScan("com.travel.mapper")
public class TravelApplication {

    public static void main(String[] args) {
        SpringApplication.run(TravelApplication.class, args);
        System.out.println("=================================");
        System.out.println("旅游讲解小程序后端服务启动成功！");
        System.out.println("Swagger文档地址: http://localhost:8080/swagger-ui.html");
        System.out.println("健康检查地址: http://localhost:8080/health");
        System.out.println("API基础地址: http://localhost:8080/");
        System.out.println("=================================");
    }
}
