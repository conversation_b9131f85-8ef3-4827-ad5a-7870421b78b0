package com.travel.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.travel.common.Result;
import com.travel.entity.Product;
import com.travel.service.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 产品控制器
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/products")
@Tag(name = "产品管理", description = "旅游产品相关接口")
@Validated
public class ProductController {

    @Autowired
    private ProductService productService;

    @Operation(summary = "分页查询产品列表", description = "支持城市、类型、关键词筛选")
    @GetMapping("/page")
    public Result<IPage<Product>> getProductPage(
            @Parameter(description = "当前页码", example = "1") @RequestParam(defaultValue = "1") @Min(1) Integer current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") @Min(1) Integer size,
            @Parameter(description = "城市ID") @RequestParam(required = false) Integer cityId,
            @Parameter(description = "产品类型(1讲解包 2景点)") @RequestParam(required = false) Integer type,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        
        log.info("分页查询产品: current={}, size={}, cityId={}, type={}, keyword={}", 
                current, size, cityId, type, keyword);
        
        IPage<Product> page = productService.getProductPage(current, size, cityId, type, keyword);
        return Result.success(page);
    }

    @Operation(summary = "获取产品详情", description = "根据产品ID获取详细信息")
    @GetMapping("/{productId}")
    public Result<Product> getProductDetail(
            @Parameter(description = "产品ID", required = true) @PathVariable @NotNull Integer productId) {
        
        log.info("获取产品详情: productId={}", productId);
        Product product = productService.getProductDetail(productId);
        return Result.success(product);
    }

    @Operation(summary = "根据城市获取产品列表", description = "获取指定城市的所有产品")
    @GetMapping("/city/{cityId}")
    public Result<List<Product>> getProductsByCity(
            @Parameter(description = "城市ID", required = true) @PathVariable @NotNull Integer cityId) {
        
        log.info("根据城市获取产品: cityId={}", cityId);
        List<Product> products = productService.getProductsByCity(cityId);
        return Result.success(products);
    }

    @Operation(summary = "根据类型获取产品列表", description = "获取指定类型的所有产品")
    @GetMapping("/type/{type}")
    public Result<List<Product>> getProductsByType(
            @Parameter(description = "产品类型(1讲解包 2景点)", required = true) @PathVariable @NotNull Integer type) {
        
        log.info("根据类型获取产品: type={}", type);
        List<Product> products = productService.getProductsByType(type);
        return Result.success(products);
    }

    @Operation(summary = "获取推荐产品", description = "获取推荐的产品列表")
    @GetMapping("/recommend")
    public Result<List<Product>> getRecommendProducts(
            @Parameter(description = "返回数量限制", example = "10") @RequestParam(defaultValue = "10") Integer limit) {
        
        log.info("获取推荐产品: limit={}", limit);
        List<Product> products = productService.getRecommendProducts(limit);
        return Result.success(products);
    }

    @Operation(summary = "根据讲师获取产品列表", description = "获取指定讲师的所有产品")
    @GetMapping("/lecturer/{lecturerId}")
    public Result<List<Product>> getProductsByLecturer(
            @Parameter(description = "讲师ID", required = true) @PathVariable @NotNull Integer lecturerId) {
        
        log.info("根据讲师获取产品: lecturerId={}", lecturerId);
        List<Product> products = productService.getProductsByLecturer(lecturerId);
        return Result.success(products);
    }
}
