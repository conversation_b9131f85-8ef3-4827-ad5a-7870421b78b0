package com.travel.controller;

import com.travel.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/health")
@Tag(name = "系统监控", description = "系统健康检查接口")
public class HealthController {

    @Operation(summary = "健康检查", description = "检查系统运行状态")
    @GetMapping
    public Result<Map<String, Object>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", LocalDateTime.now());
        healthInfo.put("service", "Travel Mini-program Backend");
        healthInfo.put("version", "1.0.0");
        
        log.info("健康检查请求");
        return Result.success("系统运行正常", healthInfo);
    }
}
