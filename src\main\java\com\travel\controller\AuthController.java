package com.travel.controller;

import com.travel.common.Result;
import com.travel.dto.LoginRequest;
import com.travel.dto.LoginResponse;
import com.travel.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 认证控制器
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@Tag(name = "认证管理", description = "用户认证相关接口")
@Validated
public class AuthController {

    @Autowired
    private UserService userService;

    @Operation(summary = "微信小程序登录", description = "通过微信授权码进行登录")
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        log.info("用户登录请求: code={}", request.getCode());
        LoginResponse response = userService.login(request);
        return Result.success("登录成功", response);
    }

    @Operation(summary = "绑定手机号", description = "绑定用户手机号")
    @PostMapping("/bind-phone")
    public Result<Void> bindPhone(
            @Parameter(description = "用户ID", required = true) @RequestParam Integer userId,
            @Parameter(description = "手机号授权码", required = true) @RequestParam @NotBlank(message = "授权码不能为空") String code) {
        log.info("绑定手机号请求: userId={}, code={}", userId, code);
        userService.bindPhone(userId, code);
        return Result.success("手机号绑定成功");
    }
}
